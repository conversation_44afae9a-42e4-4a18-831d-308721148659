<script lang="ts">
	import trackingService from '$lib/tools/tracking';
	import reportsService from '$lib/tools/reports';

	async function testError() {
		try {
			throw new Error('This is a test error message');
		} catch (error) {
			await reportsService.report(
				'🚨 Test Error Report',
				'This is a simulated error for testing purposes',
				'high',
				{
					functionName: 'testError',
					userId: '123123',
					error: error as Error,
					metadata: {
						testData: 'error simulation',
						errorType: 'simulated'
					}
				}
			);
		}
	}

	async function testWarning() {
		await reportsService.report(
			'⚠️ High Memory Usage Warning',
			'Memory usage is above the recommended threshold',
			'medium',
			{
				functionName: 'testWarning',
				userId: '123123',
				metadata: {
					memoryUsage: '85%',
					threshold: '80%',
					action: 'monitor'
				}
			}
		);
	}

	async function testInfo() {
		await reportsService.report(
			'ℹ️ User Action Completed',
			'User successfully completed the onboarding process',
			'low',
			{
				functionName: 'testInfo',
				userId: '123123',
				metadata: {
					completionTime: 'fast',
					steps: 5,
					duration: '2 minutes'
				}
			}
		);
	}

	async function testCritical() {
		await reportsService.report(
			'🔥 Critical System Error',
			'Database connection completely failed - immediate attention required',
			'critical',
			{
				functionName: 'testCritical',
				userId: '123123',
				metadata: {
					errorCode: 'DB_CONN_FAILED',
					retries: 3,
					lastAttempt: new Date().toISOString()
				}
			}
		);
	}
</script>

<h1>Welcome to SvelteKit</h1>
<p>Visit <a href="https://svelte.dev/docs/kit">svelte.dev/docs/kit</a> to read the documentation</p>

<div style="display: flex; flex-direction: column; gap: 10px; max-width: 300px;">
	<!-- Tracking buttons -->
	<button
		onclick={() =>
			trackingService.trackAction('test-track', { gozaste: 'bateman!' }, '123123', 1000)}
		>test track.</button
	>
	<button
		onclick={() =>
			trackingService.trackAction(
				'user-has-viadaged',
				{ pintoSize: 23.5 },
				'123123',
				undefined,
				true
			)}>test track discord!</button
	>

	<!-- Reports buttons -->
	<button onclick={testError} style="background-color: #ff4444;"> 🚨 Test Error Report </button>

	<button onclick={testWarning} style="background-color: #ffaa00;"> ⚠️ Test Warning Report </button>

	<button onclick={testInfo} style="background-color: #44aa44;"> ℹ️ Test Info Report </button>

	<button onclick={testCritical} style="background-color: #8b0000;">
		🔥 Test Critical Report
	</button>
</div>
